# frozen_string_literal: true

source 'https://rubygems.org'
git_source(:github) { |repo| "https://github.com/#{repo}.git" }

ruby '3.3.3'
gem 'rails', '~> 7.0.8', '>= 7.0.8.1'

gem 'aws-sdk-s3'
gem 'cancancan', '~> 3.5'
gem 'health_check'
gem 'importmap-rails'
gem 'jbuilder'
gem 'puma', '~> 5.0'
gem 'redis', '~> 5.1.0'
gem 'sprockets-rails'
gem 'stimulus-rails'
gem 'turbo-rails'

gem 'bearcat', '~> 1.5.31'
gem 'canvas_sync', '~> 0.22.18'
gem 'lti_roles', '~> 0.0.4'
gem 'miscellany', '~> 0.1.22'
gem 'panda_pal', '~> 5.13'
gem 'paul_bunyan'

gem 'rack-cors'

gem 'react-rails', '~> 3.2'
gem 'ros-apartment-sidekiq', '~> 1.2', require: 'apartment-sidekiq'
gem 'sidekiq', '~> 7.2'
gem 'sidekiq-failures'
gem 'sidekiq-scheduler', '~> 5.0'

gem 'sentry-rails'
gem 'sentry-ruby'
gem 'sentry-sidekiq'
gem 'stackprof'

gem 'pg', '~> 1.5'

gem 'bootsnap', '>= 1.4.4', require: false
gem 'mutex_m', '~> 0.2.0'
gem 'tzinfo-data', platforms: [:mri, :windows]

# Faraday extra dependency
gem 'faraday-follow_redirects', '~> 0.3.0'

group :development, :test do
  # See https://guides.rubyonrails.org/debugging_rails_applications.html#debugging-with-the-debug-gem
  gem 'brakeman', require: false
  gem 'byebug', platforms: [:mri, :windows]
  gem 'debug', platforms: [:mri, :windows]
  gem 'factory_bot_rails'
  gem 'faker'
  gem 'gergich', require: false
  gem 'pry'
  gem 'rspec-rails'
  gem 'rubocop', '~> 1.67'
end

group :development do
  # Use console on exceptions pages [https://github.com/rails/web-console]
  gem 'web-console'

  # Add speed badges [https://github.com/MiniProfiler/rack-mini-profiler]
  # gem "rack-mini-profiler"

  # Speed up commands on slow machines / big apps [https://github.com/rails/spring]
  # gem "spring"

  gem 'thin', '~> 2.0'
end

group :test do
  # Use system testing [https://guides.rubyonrails.org/testing.html#system-testing]
  gem 'capybara'
  gem 'selenium-webdriver'
  gem 'shoulda'
  gem 'shoulda-matchers'
end

gem 'react_on_rails', '= 14.2'
gem 'shakapacker', '= 8.3'

# TODO: Downgrade rubyzip from 3.0.1 to 2.3.2 as canvas_sync is failing when extracting zip files
gem 'rubyzip', '2.3.2'
