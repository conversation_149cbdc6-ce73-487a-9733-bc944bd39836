# Stride Calendar Viewer

### Customer: Stride, Inc

### [SSD Link](https://docs.google.com/document/d/14_QK0xQLDdhrJwkSOim40idThlxPnAz0bSXA6tZjLK0/edit?tab=t.0)
### [UI Mockups](https://www.figma.com/design/nqZDPRg3uolVhu7WYOogS1/Instructure---Stride---Student-Observation---Present?node-id=18-854&p=f&t=H4Xkkt9NvlYyXvXT-0)


In this Tool course, both Course-Level and Account-Level admins can create a temporary Canvas observer link for an active student. This allows them to observe the student and view the student's calendar view.

## Application Structure

This application is built with:
- **Rails** - Backend framework
- **Sidekiq** - Background job processing
- **PostgreSQL** - Database
- **Shakapacker** - Asset bundling for React components
- **React** - Frontend components

## Canvas Configuration Requirements

### Required Reports
This application requires the following reports to be enabled in Canvas:

- **proservices_provisioning_csv**

## Application Setup

1. Ensure that all packages are installed:
   - Run `yarn` to install JavaScript dependencies.
   - Run `bundle` to install Ruby dependencies.

2. Create and migrate the database:
   ```
   bundle exec rake db:create db:migrate
   ```

   If there are pending migrations, run:
   ```
   bundle exec rake db:migrate
   ```

3. Build the JavaScript files:
   ```
   bin/shakapacker-dev-server
   ```

   This will keep Shakapacker running and watching for changes to source files.

4. Generate an API token:
   - Go to `http://localhost:3000/profile/settings` and generate a 'New Access Token'.
   - Use this token in the Canvas settings as the `api_token`.

5. Create PandaPal Organization:
   This is a fairly standard tool. It needs Canvas credentials. See `config/panda_pal.rb` for settings schema.

   ```ruby
   PandaPal::Organization.create(
     name: 'local',
     canvas_account_id: 1, # Canvas Account Id
     key: SecureRandom.hex(16),
     secret: SecureRandom.hex(16),
     salesforce_id: 1,
     settings: {
       canvas: {
         base_url: 'http://localhost:3000', # Canvas Instance URL
         api_token: 'CANVAS-API-TOKEN' # Canvas API Token
       }
     }
   )
   ```

6. Add Application to Canvas

   Run following in console to install application:
   ```ruby
   org = PandaPal::Organization.find_by_name!('local')
   org.install_lti(
     host: "http://localhost:4000", # LTI host URL
     context: "account/self", # (Optional) Or "account/3", "course/1", etc
     exists: :error, # (Optional) Action to take if an LTI with the same Key already exists. Options are :error, :replace, :duplicate, :update
     version: "v1p3", # (Optional, default `v1p3`) LTI Version. Accepts `v1p0` or `v1p3`.
     dedicated_deployment: false, # (Optional) If true, the Organization will be updated to link to a single deployment rather then to the general LTI Key. (experimental, LTI 1.3 only)
   )
   ```

## Running the Application

You can launch the application using either of these methods:

### Option 1: Using bin/dev (Recommended)
```bash
./bin/dev
```

This command will start all necessary services (Rails server, Sidekiq, and Shakapacker) in a single process using Foreman.

### Option 2: Manual startup
Start each service separately:
1. Start the Rails server: `bundle exec rails server`
2. Start Sidekiq: `bundle exec sidekiq`
3. Start Shakapacker: `bin/shakapacker-dev-server`

## Testing

Run the test suite using RSpec:
```bash
bundle exec rspec
```
