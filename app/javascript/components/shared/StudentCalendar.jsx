import React, { useState, useEffect } from 'react'
import {
  <PERSON>,
  Flex,
  <PERSON>,
  <PERSON><PERSON>,
  Al<PERSON>,
  Spinner
} from '@instructure/ui'
import { IconClockLine, IconUserLine } from '@instructure/ui-icons'

const StudentCalendar = ({
  observerLink,
  onEndLink,
  onRenewLink,
  onExpired,
  onShowStopModal,
  loading = false
}) => {
    // calculate seconds remaining instead of minutes
  const [timeRemaining, setTimeRemaining] = useState(
    Math.max(
      0,
      Math.floor(
        (new Date(observerLink?.expires_at) - new Date()) / 1000
      )
    )
  )

    useEffect(() => {
    if (!observerLink) return

    // update every second instead of every minute
    const timer = setInterval(() => {
      const now = new Date()
      const expiresAt = new Date(observerLink.expires_at)
      const remaining = Math.max(
        0,
        Math.floor((expiresAt - now) / 1000)
      )

      // Check if timer just expired
      if (timeRemaining > 0 && remaining === 0 && onExpired) {
        onExpired()
      }

      setTimeRemaining(remaining)
    }, 1000)

    return () => clearInterval(timer)
  }, [observerLink, timeRemaining, onExpired])

    // formatter outputs MM:SS instead of words
  const formatTimeRemaining = (seconds) => {
    if (seconds <= 0) return 'Expired'

    const minutes = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${minutes.toString().padStart(2, '0')}:${secs
      .toString()
      .padStart(2, '0')}`
  }

  const getStatusVariant = () => {
    if (timeRemaining <= 0) return 'error'
    if (timeRemaining <= 10) return 'warning'
    return 'success'
  }

  if (!observerLink) return null

  return (
    <View>
      <Flex justifyItems="space-between" margin="0 0 large 0">
        <Flex.Item shouldGrow>
          <Text size="large" weight="bold">Student Observer Tool</Text>
        </Flex.Item>
      </Flex>

      <Alert variant={getStatusVariant()} margin="0 0 large 0" renderCloseButtonLabel="Close" timeout={5000}>
        <Flex direction="column" gap="small">
          <Text weight="bold">
            {timeRemaining <= 0 ? 'Observer Link Expired' : 'Active Observer Link'}
          </Text>
          <Flex alignItems="center" gap="small">
            <IconClockLine />
            <Text>
              Time remaining: {formatTimeRemaining(timeRemaining)}
            </Text>
          </Flex>
        </Flex>
      </Alert>

      <View
        as="div"
        background="primary"
        padding="medium"
        borderRadius="medium"
        margin="0 0 large 0"
      >
        <Flex direction="column" gap="medium">
          <Text size="large" weight="bold" color="primary-inverse">
            Currently Observing
          </Text>

          <Flex alignItems="center" gap="small">
            <IconUserLine color="primary-inverse" />
            <Flex direction="column" gap="x-small">
              <Text color="primary-inverse" weight="bold">
                {observerLink.observed_student.sortable_name}
              </Text>
              <Text color="primary-inverse" size="small">
                SIS ID: {observerLink.observed_student.sis_id || 'N/A'}
              </Text>
            </Flex>
          </Flex>

          <Flex direction="column" gap="x-small">
            <Text color="primary-inverse" size="small">
              <strong>Created:</strong> {new Date(observerLink.created_at).toLocaleString()}
            </Text>
            <Text color="primary-inverse" size="small">
              <strong>Expires:</strong> {new Date(observerLink.expires_at).toLocaleString()}
            </Text>
            {observerLink.renewed_at && (
              <Text color="primary-inverse" size="small">
                <strong>Renewed:</strong> {new Date(observerLink.renewed_at).toLocaleString()}
              </Text>
            )}
          </Flex>
        </Flex>
      </View>

      <Flex
        justifyItems="space-between"
        alignItems="center"
        margin="large 0 0 0"
      >
        <Flex.Item>
          Link Status{' '}
          <Text
            color={timeRemaining > 0 ? 'success' : 'danger'}
            weight="bold"
          >
            {observerLink.status}
          </Text>
        </Flex.Item>

        <Flex.Item>
          <Flex alignItems="center" gap="medium">
            {/* show timer inline in MM:SS format */}
            <Text weight="bold">
              Time Remaining {formatTimeRemaining(timeRemaining)}
            </Text>

            {observerLink.can_be_renewed && timeRemaining > 0 && (
              <Button
                color="secondary"
                onClick={onRenewLink}
                disabled={loading}
              >
                {loading ? (
                  <Flex alignItems="center" gap="x-small">
                    <Spinner size="x-small" renderTitle="Renewing..." />
                    <Text>Renewing...</Text>
                  </Flex>
                ) : (
                  'Renew Link'
                )}
              </Button>
            )}

            <Button
              color="danger"
              onClick={onShowStopModal || onEndLink}
              disabled={loading}
            >
              {loading ? (
                <Flex alignItems="center" gap="x-small">
                  <Spinner size="x-small" renderTitle="Ending link..." />
                  <Text>Ending...</Text>
                </Flex>
              ) : (
                'Stop Observing'
              )}
            </Button>
          </Flex>
        </Flex.Item>
      </Flex>

      {timeRemaining <= 0 && (
        <Alert variant="info" margin="large 0 0 0">
          <Text>
            This observer link has expired. Click "End Observer Link" to return to the student list.
          </Text>
        </Alert>
      )}
    </View>
  )
}

export default StudentCalendar
