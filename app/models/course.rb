# frozen_string_literal: true

# #
# AUTO GENERATED MODEL
# This model was auto generated by the CanvasSync Gem.
# You can customize it as needed, but make sure you test
# any changes you make to the auto generated methods.
#

class Course < ApplicationRecord
  include CanvasSync::Record
  include CanvasSync::Concerns::ApiSyncable

  canvas_sync_features :defaults

  # include CanvasSync::Concerns::LiveEventSync
  # after_process_live_event do
  #   if account.nil?
  #     acc = Account.new(canvas_id: canvas_account_id)
  #     acc.sync_from_api
  #   end
  # end

  validates :canvas_id, uniqueness: true, presence: true
  belongs_to :term, foreign_key: :canvas_term_id, primary_key: :canvas_id, optional: true
  has_many :enrollments, primary_key: :canvas_id, foreign_key: :canvas_course_id
  has_many :sections, primary_key: :canvas_id, foreign_key: :canvas_course_id
  has_many :assignments, as: :context, primary_key: :canvas_id, foreign_key: :canvas_context_id, foreign_type: :canvas_context_type
  has_many :submissions, primary_key: :canvas_id, foreign_key: :canvas_course_id
  has_many :assignment_groups, primary_key: :canvas_id, foreign_key: :canvas_course_id
  has_many :groups, primary_key: :canvas_id, foreign_key: :canvas_course_id
  has_many :rubrics, as: :context, primary_key: :canvas_id, foreign_key: :canvas_context_id, foreign_type: :canvas_context_type

  api_syncable({
                 sis_id: :sis_course_id,
                 course_code: :course_code,
                 name: :name,
                 workflow_state: :workflow_state,
                 canvas_term_id: :enrollment_term_id,
                 canvas_account_id: :account_id,
                 start_at: :start_at,
                 end_at: :end_at
               }, ->(api) { api.course(canvas_id) })
end
